package com.example.spatialsdk

import org.junit.Test
import org.junit.Assert.*
import com.example.spatialsdk.components.*
import com.example.spatialsdk.interaction.*

/**
 * Example local unit test, which will execute on the development machine (host).
 *
 * See [testing documentation](http://d.android.com/tools/testing).
 */
class ExampleUnitTest {
    @Test
    fun addition_isCorrect() {
        assertEquals(4, 2 + 2)
    }
    
    @Test
    fun vector3_operations_work() {
        val v1 = Vector3(1f, 2f, 3f)
        val v2 = Vector3(4f, 5f, 6f)
        
        val sum = v1 + v2
        assertEquals(5f, sum.x, 0.001f)
        assertEquals(7f, sum.y, 0.001f)
        assertEquals(9f, sum.z, 0.001f)
        
        val scaled = v1 * 2f
        assertEquals(2f, scaled.x, 0.001f)
        assertEquals(4f, scaled.y, 0.001f)
        assertEquals(6f, scaled.z, 0.001f)
    }
    
    @Test
    fun entity_component_system_works() {
        val entity = Entity.create()
        
        val transform = Transform(
            translation = Vector3(1f, 2f, 3f),
            scale = Vector3.one()
        )
        
        entity.setComponent(transform)
        
        assertTrue(entity.hasComponent(Transform::class.java))
        
        val retrievedTransform = entity.getComponent(Transform::class.java)
        assertNotNull(retrievedTransform)
        assertEquals(1f, retrievedTransform?.translation?.x ?: 0f, 0.001f)
    }
    
    @Test
    fun gesture_detection_works() {
        val gestureDetector = GestureDetector()
        
        // Create a hand with extended index finger
        val indexFinger = Finger(
            type = FingerType.Index,
            joints = emptyList(),
            isExtended = true
        )
        
        val otherFingers = listOf(
            Finger(FingerType.Middle, emptyList(), false),
            Finger(FingerType.Ring, emptyList(), false),
            Finger(FingerType.Pinky, emptyList(), false)
        )
        
        val hand = Hand(
            isTracked = true,
            fingers = listOf(indexFinger) + otherFingers
        )
        
        val gestures = gestureDetector.detectGestures(hand)
        assertTrue(gestures.isPointing)
        assertFalse(gestures.isFist)
    }
    
    @Test
    fun scene_management_works() {
        val scene = Scene()
        val entity = Entity.create()
        
        scene.addEntity(entity)
        
        val entities = scene.getEntities()
        assertEquals(1, entities.size)
        assertTrue(entities.contains(entity))
        
        scene.removeEntity(entity)
        assertEquals(0, scene.getEntities().size)
    }
}
