#!/bin/bash

# Meta Spatial SDK Demo Build Script
# This script helps build and deploy the Spatial SDK demo application

set -e

echo "🚀 Meta Spatial SDK Demo Build Script"
echo "======================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Android SDK is available
check_android_sdk() {
    print_status "Checking Android SDK..."
    if ! command -v adb &> /dev/null; then
        print_error "ADB not found. Please install Android SDK and add it to PATH."
        exit 1
    fi
    print_success "Android SDK found"
}

# Check if device is connected
check_device() {
    print_status "Checking for connected devices..."
    DEVICES=$(adb devices | grep -v "List of devices" | grep "device$" | wc -l)
    if [ $DEVICES -eq 0 ]; then
        print_warning "No devices connected. Please connect your Meta Quest device."
        print_status "Make sure:"
        print_status "  1. Developer mode is enabled on your Quest"
        print_status "  2. USB debugging is enabled"
        print_status "  3. Device is connected via USB"
        exit 1
    fi
    print_success "Device connected"
}

# Clean build
clean_build() {
    print_status "Cleaning previous build..."
    ./gradlew clean
    print_success "Clean completed"
}

# Build APK
build_apk() {
    print_status "Building APK..."
    ./gradlew assembleDebug
    print_success "APK built successfully"
}

# Install APK
install_apk() {
    print_status "Installing APK to device..."
    ./gradlew installDebug
    print_success "APK installed successfully"
}

# Launch app
launch_app() {
    print_status "Launching application..."
    adb shell am start -n com.example.spatialsdk/.MainActivity
    print_success "Application launched"
}

# Show logs
show_logs() {
    print_status "Showing application logs (Ctrl+C to stop)..."
    adb logcat | grep -E "(SpatialSDK|MainActivity|SPATIAL)"
}

# Main menu
show_menu() {
    echo ""
    echo "Please select an option:"
    echo "1) Full build and deploy"
    echo "2) Clean build"
    echo "3) Build APK only"
    echo "4) Install APK"
    echo "5) Launch app"
    echo "6) Show logs"
    echo "7) Check device status"
    echo "8) Exit"
    echo ""
}

# Process menu choice
process_choice() {
    case $1 in
        1)
            check_android_sdk
            check_device
            clean_build
            build_apk
            install_apk
            launch_app
            print_success "Full deployment completed!"
            ;;
        2)
            clean_build
            build_apk
            ;;
        3)
            build_apk
            ;;
        4)
            check_device
            install_apk
            ;;
        5)
            check_device
            launch_app
            ;;
        6)
            check_device
            show_logs
            ;;
        7)
            check_android_sdk
            check_device
            print_success "Device is ready for development"
            ;;
        8)
            print_status "Goodbye!"
            exit 0
            ;;
        *)
            print_error "Invalid option. Please try again."
            ;;
    esac
}

# Main execution
main() {
    # Check if gradlew exists
    if [ ! -f "./gradlew" ]; then
        print_error "gradlew not found. Please run this script from the project root directory."
        exit 1
    fi
    
    # Make gradlew executable
    chmod +x ./gradlew
    
    # If arguments provided, process them
    if [ $# -gt 0 ]; then
        case $1 in
            --build)
                check_android_sdk
                check_device
                clean_build
                build_apk
                install_apk
                launch_app
                ;;
            --clean)
                clean_build
                ;;
            --install)
                check_device
                install_apk
                ;;
            --launch)
                check_device
                launch_app
                ;;
            --logs)
                check_device
                show_logs
                ;;
            --help)
                echo "Usage: $0 [option]"
                echo "Options:"
                echo "  --build   Full build and deploy"
                echo "  --clean   Clean build"
                echo "  --install Install APK"
                echo "  --launch  Launch app"
                echo "  --logs    Show logs"
                echo "  --help    Show this help"
                ;;
            *)
                print_error "Unknown option: $1"
                print_status "Use --help for available options"
                ;;
        esac
        return
    fi
    
    # Interactive mode
    while true; do
        show_menu
        read -p "Enter your choice [1-8]: " choice
        process_choice $choice
        echo ""
        read -p "Press Enter to continue..."
    done
}

# Run main function
main "$@"
