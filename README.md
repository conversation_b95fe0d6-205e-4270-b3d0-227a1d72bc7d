# Meta Spatial SDK Demo Application (模拟版本)

这是一个完整的 Meta Spatial SDK 示例应用架构演示，展示了如何设计和构建沉浸式空间计算应用。

**注意：这是一个架构演示版本，使用模拟的 Meta Spatial SDK API。要运行真实的 Meta Spatial SDK 应用，需要：**
1. 获取 Meta 开发者账号和 SDK 访问权限
2. 下载官方 Meta Spatial SDK
3. 替换模拟的 API 调用为真实的 SDK 调用

## 功能特性

### 核心功能
- ✅ **空间环境管理** - 完整的 3D 场景管理系统
- ✅ **手部追踪** - 高精度手部追踪和手势识别
- ✅ **物理交互** - 基于物理的对象交互系统
- ✅ **空间音频** - 3D 空间化音频支持
- ✅ **混合现实** - VR 和 AR 模式切换
- ✅ **Jetpack Compose 集成** - 现代 Android UI 框架支持

### 交互功能
- 🤏 **手势识别** - 捏取、指向、抓取、拳头等手势
- 🎯 **对象交互** - 抓取、释放、悬停交互
- 🎮 **物理模拟** - 真实的物理碰撞和重力
- 🔊 **空间音频** - 位置相关的 3D 音效

### 渲染功能
- 🌟 **PBR 渲染** - 基于物理的渲染管线
- 💡 **动态光照** - 方向光、环境光支持
- 🎨 **材质系统** - 金属度、粗糙度、发光等属性
- 🎬 **动画系统** - 关键帧动画和程序化动画

## 项目结构

```
app/
├── src/main/java/com/example/spatialsdk/
│   ├── MainActivity.kt                 # 主活动
│   ├── components/
│   │   └── SpatialComponents.kt        # ECS 组件系统
│   ├── core/
│   │   └── SpatialEnvironment.kt       # 空间环境管理
│   ├── interaction/
│   │   └── HandTrackingSystem.kt       # 手部追踪系统
│   ├── scenes/
│   │   └── DemoScene.kt               # 示例场景
│   └── ui/theme/                      # UI 主题
├── src/main/res/                      # 资源文件
└── build.gradle.kts                   # 构建配置
```

## 系统要求

### 硬件要求
- Meta Quest 2/3/3S/Pro 头显设备
- Meta Quest build v69.0 或更新版本

### 开发环境
- Android Studio Hedgehog 或更新版本
- JDK 8 或更高版本
- Android SDK API 24 (Android 7.0) 或更高
- Meta Spatial Editor

### 依赖项
- **模拟的 Meta Spatial SDK API** (架构演示)
- Jetpack Compose
- Kotlin 1.9.10
- Android Gradle Plugin 8.2.0
- Kotlin Coroutines

## 快速开始

### 1. 环境准备

1. 安装 Android Studio Hedgehog 或更新版本
2. 下载并安装 [Meta Spatial Editor](https://developers.meta.com/horizon/downloads/spatial-sdk/)
3. 确保 Meta Quest 设备已启用开发者模式

### 2. 项目构建

```bash
# 克隆项目
git clone <repository-url>
cd spatial_sdk_c

# 在 Android Studio 中打开项目
# 或使用命令行构建
./gradlew assembleDebug
```

### 3. 部署到设备

1. 通过 USB 连接 Meta Quest 设备
2. 在 Android Studio 中点击 "Run" 按钮
3. 或使用 ADB 安装：
```bash
adb install app/build/outputs/apk/debug/app-debug.apk
```

### 4. 运行应用

1. 戴上 Meta Quest 头显
2. 在 "Unknown Sources" 中找到应用
3. 启动 "Spatial SDK Demo" 应用

## 使用指南

### 基础操作

1. **启动应用** - 应用会显示 2D 面板界面
2. **进入沉浸模式** - 点击 "Enter Immersive Mode" 按钮
3. **手部交互** - 使用手部追踪与 3D 对象交互
4. **退出沉浸模式** - 点击右上角的 "Exit" 按钮

### 手势操作

- **捏取手势** - 拇指和食指靠近进行精确操作
- **抓取手势** - 握拳抓取对象
- **指向手势** - 伸出食指进行指向
- **悬停** - 手部靠近对象触发悬停效果

### 场景内容

- **彩色立方体** - 6 个可交互的彩色立方体，支持抓取
- **浮动球体** - 3 个带动画的橙色球体
- **交互面板** - 虚拟屏幕面板，支持悬停交互
- **旋转圆柱** - 带动画的紫色圆柱体
- **环境音效** - 背景环境音和空间化音频

## 架构说明

### ECS 架构

应用采用 Entity-Component-System (ECS) 架构：

- **Entity** - 场景中的对象实体
- **Component** - 数据组件（位置、渲染、物理等）
- **System** - 处理逻辑系统（渲染、物理、交互等）

### 核心系统

1. **SpatialEnvironment** - 空间环境管理器
2. **HandTrackingManager** - 手部追踪管理器
3. **InteractionSystem** - 交互系统
4. **RenderingEngine** - 渲染引擎
5. **PhysicsEngine** - 物理引擎
6. **AudioEngine** - 音频引擎

### 组件类型

- **Transform** - 位置、旋转、缩放
- **MeshComponent** - 3D 网格和材质
- **RigidBodyComponent** - 物理刚体
- **InteractableComponent** - 交互行为
- **AnimationComponent** - 动画控制
- **AudioSourceComponent** - 音频源

## 自定义开发

### 添加新的 3D 对象

```kotlin
val entity = Entity.create()

entity.setComponent(
    MeshComponent(
        mesh = PrimitiveMesh.createSphere(),
        material = Material.createPBR().apply {
            baseColor = Color.Red
            metallic = 0.5f
            roughness = 0.3f
        }
    )
)

entity.setComponent(
    Transform(
        translation = Vector3(0f, 1f, -2f),
        scale = Vector3(0.5f, 0.5f, 0.5f)
    )
)

scene.addEntity(entity)
```

### 添加交互行为

```kotlin
entity.setComponent(
    InteractableComponent(
        onGrab = { println("Object grabbed!") },
        onRelease = { println("Object released!") },
        onHover = { println("Object hovered!") }
    )
)
```

### 添加物理属性

```kotlin
entity.setComponent(
    RigidBodyComponent(
        mass = 1.0f,
        isKinematic = false,
        useGravity = true
    )
)
```

## 故障排除

### 常见问题

1. **应用无法启动**
   - 检查 Meta Quest 设备版本是否 >= v69.0
   - 确认开发者模式已启用
   - 检查 USB 调试连接

2. **手部追踪不工作**
   - 在 Quest 设置中启用手部追踪
   - 确保光线充足
   - 检查手部追踪权限

3. **构建失败**
   - 检查 Android Studio 版本
   - 确认 Meta Spatial SDK 依赖正确
   - 清理并重新构建项目

### 调试技巧

1. **查看日志**
```bash
adb logcat | grep "SpatialSDK"
```

2. **性能监控**
```bash
adb shell dumpsys gfxinfo com.example.spatialsdk
```

3. **手部追踪调试**
- 启用 Quest 开发者选项中的手部追踪可视化

## 扩展功能

### 计划中的功能
- [ ] 多用户协作
- [ ] 网络同步
- [ ] 自定义着色器
- [ ] 高级动画系统
- [ ] 场景编辑器
- [ ] 资源管理系统

### 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 相关资源

- [Meta Spatial SDK 官方文档](https://developers.meta.com/horizon/develop/spatial-sdk)
- [Meta Spatial SDK 示例](https://github.com/meta-quest/Meta-Spatial-SDK-Samples)
- [Meta 开发者社区](https://communityforums.atmeta.com/)
- [Android 开发文档](https://developer.android.com/)

## 联系方式

如有问题或建议，请通过以下方式联系：

- 创建 GitHub Issue
- 发送邮件至开发团队
- 加入 Meta 开发者社区讨论
