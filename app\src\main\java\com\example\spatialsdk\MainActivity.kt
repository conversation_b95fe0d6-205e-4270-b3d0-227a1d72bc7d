package com.example.spatialsdk

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.spatialsdk.ui.theme.SpatialSDKTheme
import com.example.spatialsdk.core.SpatialEnvironment
import com.example.spatialsdk.components.*
import com.example.spatialsdk.interaction.Hand
import com.example.spatialsdk.scenes.SceneManager

class MainActivity : ComponentActivity() {

    private lateinit var spatialEnvironment: SpatialEnvironment
    private var isImmersiveMode by mutableStateOf(false)

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Initialize Spatial SDK
        initializeSpatialSDK()

        setContent {
            SpatialSDKTheme {
                if (isImmersiveMode) {
                    ImmersiveContent()
                } else {
                    PanelContent()
                }
            }
        }
    }

    private fun initializeSpatialSDK() {
        try {
            // Create spatial environment
            spatialEnvironment = SpatialEnvironment.create(this)

            // Set up the scene
            setupScene()

            // Enable hand tracking
            enableHandTracking()

        } catch (e: Exception) {
            // Handle initialization error
            println("Failed to initialize Spatial SDK: ${e.message}")
        }
    }

    private fun setupScene() {
        // Create demo scene using SceneManager
        val sceneManager = SceneManager()
        val demoScene = sceneManager.createDemoScene()

        // Set the scene in spatial environment
        spatialEnvironment.scene = demoScene

        // Additional setup
        createHelloWorldText(demoScene)
    }

    private fun createHelloWorldText(scene: Scene) {
        // Create 3D text entity
        val textEntity = Entity.create()

        // Add text component
        textEntity.setComponent(
            TextComponent(
                text = "Hello Spatial SDK!",
                fontSize = 0.5f,
                color = Color.White
            )
        )

        // Position the text
        textEntity.setComponent(
            Transform(
                translation = Vector3(0f, 2f, -3f),
                rotation = Quaternion.identity(),
                scale = Vector3.one()
            )
        )

        // Add to scene
        scene.addEntity(textEntity)
    }

    // Note: Interactive objects and lighting are now handled by DemoScene

    private fun enableHandTracking() {
        spatialEnvironment.handTracker?.let { handTracker ->
            handTracker.isEnabled = true
            handTracker.onHandUpdate = { leftHand, rightHand ->
                // Handle hand tracking updates
                handleHandTracking(leftHand, rightHand)
            }
        }
    }

    private fun handleHandTracking(leftHand: Hand?, rightHand: Hand?) {
        // Process hand tracking data
        leftHand?.let { hand ->
            if (hand.isTracked) {
                // Update left hand interactions
                processHandInteraction(hand, "left")
            }
        }

        rightHand?.let { hand ->
            if (hand.isTracked) {
                // Update right hand interactions
                processHandInteraction(hand, "right")
            }
        }
    }

    private fun processHandInteraction(hand: Hand, handType: String) {
        // Check for pinch gesture
        if (hand.isPinching) {
            println("$handType hand is pinching")
            // Implement pinch interaction logic
        }

        // Check for pointing gesture
        if (hand.isPointing) {
            println("$handType hand is pointing")
            // Implement pointing interaction logic
        }
    }

    @Composable
    private fun PanelContent() {
        Surface(
            modifier = Modifier.fillMaxSize(),
            color = MaterialTheme.colorScheme.background
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                Text(
                    text = "Spatial SDK Demo",
                    fontSize = 24.sp,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.primary
                )

                Spacer(modifier = Modifier.height(16.dp))

                Text(
                    text = "Welcome to Meta Spatial SDK",
                    fontSize = 16.sp,
                    color = MaterialTheme.colorScheme.onBackground
                )

                Spacer(modifier = Modifier.height(32.dp))

                Button(
                    onClick = {
                        isImmersiveMode = true
                        enterImmersiveMode()
                    },
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text("Enter Immersive Mode")
                }

                Spacer(modifier = Modifier.height(16.dp))

                OutlinedButton(
                    onClick = {
                        // Show app info
                        showAppInfo()
                    },
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text("App Info")
                }
            }
        }
    }

    @Composable
    private fun ImmersiveContent() {
        // Immersive mode UI (minimal overlay)
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.TopEnd
        ) {
            Button(
                onClick = {
                    isImmersiveMode = false
                    exitImmersiveMode()
                },
                modifier = Modifier.padding(16.dp)
            ) {
                Text("Exit")
            }
        }
    }

    private fun enterImmersiveMode() {
        spatialEnvironment.setImmersiveMode(true)
    }

    private fun exitImmersiveMode() {
        spatialEnvironment.setImmersiveMode(false)
    }

    private fun showAppInfo() {
        // Implement app info display
        println("Spatial SDK Demo App v1.0")
    }

    override fun onResume() {
        super.onResume()
        spatialEnvironment.onResume()
    }

    override fun onPause() {
        super.onPause()
        spatialEnvironment.onPause()
    }

    override fun onDestroy() {
        super.onDestroy()
        spatialEnvironment.cleanup()
    }
}
