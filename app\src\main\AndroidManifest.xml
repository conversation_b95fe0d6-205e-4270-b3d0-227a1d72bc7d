<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- VR and Spatial SDK permissions -->
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="com.oculus.permission.HAND_TRACKING" />
    <uses-permission android:name="com.oculus.permission.EYE_TRACKING" />
    <uses-permission android:name="com.oculus.permission.FACE_TRACKING" />
    <uses-permission android:name="com.oculus.permission.SCENE" />
    <uses-permission android:name="com.oculus.permission.USE_ANCHOR_API" />

    <!-- VR features -->
    <uses-feature
        android:name="android.hardware.vr.headtracking"
        android:required="true"
        android:version="1" />
    <uses-feature
        android:name="oculus.software.handtracking"
        android:required="false" />
    <uses-feature
        android:name="oculus.software.eye_tracking"
        android:required="false" />
    <uses-feature
        android:name="oculus.software.face_tracking"
        android:required="false" />

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@android:style/Theme.Black.NoTitleBar.Fullscreen"
        tools:targetApi="31">

        <!-- VR mode configuration -->
        <meta-data
            android:name="com.oculus.vr.focusaware"
            android:value="true" />
        <meta-data
            android:name="com.oculus.supportedDevices"
            android:value="quest|quest2|questpro|quest3|quest3s" />
        <meta-data
            android:name="com.oculus.handtracking.frequency"
            android:value="HIGH" />
        <meta-data
            android:name="com.oculus.handtracking.version"
            android:value="V2.0" />

        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:screenOrientation="landscape"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:theme="@android:style/Theme.Black.NoTitleBar.Fullscreen">
            
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
                <category android:name="com.oculus.intent.category.VR" />
            </intent-filter>
        </activity>
    </application>
</manifest>
