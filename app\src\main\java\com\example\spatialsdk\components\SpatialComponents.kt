package com.example.spatialsdk.components

import androidx.compose.ui.graphics.Color
import com.meta.spatial.core.*

/**
 * Custom components for Spatial SDK
 */

// Text Component for 3D text rendering
data class TextComponent(
    val text: String,
    val fontSize: Float = 1.0f,
    val color: Color = Color.White,
    val fontWeight: FontWeight = FontWeight.Normal
) : Component

// Mesh Component for 3D objects
data class MeshComponent(
    val mesh: Mesh,
    val material: Material
) : Component

// Transform Component for positioning
data class Transform(
    val translation: Vector3 = Vector3.zero(),
    val rotation: Quaternion = Quaternion.identity(),
    val scale: Vector3 = Vector3.one()
) : Component

// Rigid Body Component for physics
data class RigidBodyComponent(
    val mass: Float = 1.0f,
    val isKinematic: Boolean = false,
    val useGravity: Boolean = true
) : Component

// Interactable Component for user interactions
data class InteractableComponent(
    val onGrab: (() -> Unit)? = null,
    val onRelease: (() -> Unit)? = null,
    val onHover: (() -> Unit)? = null,
    val onUnhover: (() -> Unit)? = null
) : Component

// Directional Light Component
data class DirectionalLightComponent(
    val color: Color = Color.White,
    val intensity: Float = 1.0f,
    val castShadows: Boolean = false
) : Component

// Animation Component
data class AnimationComponent(
    val animationClip: AnimationClip,
    val isPlaying: Boolean = false,
    val loop: Boolean = true,
    val speed: Float = 1.0f
) : Component

// Audio Source Component
data class AudioSourceComponent(
    val audioClip: AudioClip,
    val volume: Float = 1.0f,
    val pitch: Float = 1.0f,
    val spatialize: Boolean = true,
    val autoPlay: Boolean = false
) : Component

/**
 * Utility classes and data structures
 */

// Vector3 for 3D positions and scales
data class Vector3(
    val x: Float,
    val y: Float,
    val z: Float
) {
    companion object {
        fun zero() = Vector3(0f, 0f, 0f)
        fun one() = Vector3(1f, 1f, 1f)
        fun up() = Vector3(0f, 1f, 0f)
        fun forward() = Vector3(0f, 0f, -1f)
        fun right() = Vector3(1f, 0f, 0f)
    }
    
    operator fun plus(other: Vector3) = Vector3(x + other.x, y + other.y, z + other.z)
    operator fun minus(other: Vector3) = Vector3(x - other.x, y - other.y, z - other.z)
    operator fun times(scalar: Float) = Vector3(x * scalar, y * scalar, z * scalar)
    
    fun magnitude() = kotlin.math.sqrt(x * x + y * y + z * z)
    fun normalized() = this * (1f / magnitude())
}

// Quaternion for rotations
data class Quaternion(
    val x: Float,
    val y: Float,
    val z: Float,
    val w: Float
) {
    companion object {
        fun identity() = Quaternion(0f, 0f, 0f, 1f)
        
        fun fromEuler(pitch: Float, yaw: Float, roll: Float): Quaternion {
            val cy = kotlin.math.cos(yaw * 0.5)
            val sy = kotlin.math.sin(yaw * 0.5)
            val cp = kotlin.math.cos(pitch * 0.5)
            val sp = kotlin.math.sin(pitch * 0.5)
            val cr = kotlin.math.cos(roll * 0.5)
            val sr = kotlin.math.sin(roll * 0.5)
            
            return Quaternion(
                x = (sr * cp * cy - cr * sp * sy).toFloat(),
                y = (cr * sp * cy + sr * cp * sy).toFloat(),
                z = (cr * cp * sy - sr * sp * cy).toFloat(),
                w = (cr * cp * cy + sr * sp * sy).toFloat()
            )
        }
    }
}

// Material for rendering
data class Material(
    val baseColor: Color = Color.White,
    val metallic: Float = 0.0f,
    val roughness: Float = 0.5f,
    val emission: Color = Color.Black,
    val texture: Texture? = null
) {
    companion object {
        fun createPBR() = Material()
        fun createUnlit(color: Color) = Material(baseColor = color, metallic = 0f, roughness = 1f)
    }
}

// Primitive mesh creation utilities
object PrimitiveMesh {
    fun createCube(): Mesh {
        // Implementation would create cube mesh data
        return Mesh("cube")
    }
    
    fun createSphere(): Mesh {
        // Implementation would create sphere mesh data
        return Mesh("sphere")
    }
    
    fun createPlane(): Mesh {
        // Implementation would create plane mesh data
        return Mesh("plane")
    }
    
    fun createCylinder(): Mesh {
        // Implementation would create cylinder mesh data
        return Mesh("cylinder")
    }
}

// Basic mesh class
data class Mesh(val name: String)

// Texture class
data class Texture(val path: String)

// Animation clip
data class AnimationClip(val name: String, val duration: Float)

// Audio clip
data class AudioClip(val path: String)

// Ambient light
data class AmbientLight(
    val color: Color,
    val intensity: Float
)

// Font weight enum
enum class FontWeight {
    Normal, Bold, Light
}

// Base component interface
interface Component

// Entity class for ECS
class Entity private constructor(val id: Long) {
    private val components = mutableMapOf<Class<out Component>, Component>()
    
    companion object {
        private var nextId = 0L
        fun create(): Entity = Entity(nextId++)
    }
    
    fun <T : Component> setComponent(component: T) {
        components[component::class.java] = component
    }
    
    @Suppress("UNCHECKED_CAST")
    fun <T : Component> getComponent(type: Class<T>): T? {
        return components[type] as? T
    }
    
    fun <T : Component> hasComponent(type: Class<T>): Boolean {
        return components.containsKey(type)
    }
    
    fun removeComponent(type: Class<out Component>) {
        components.remove(type)
    }
}

// Scene class for managing entities
class Scene {
    private val entities = mutableListOf<Entity>()
    var ambientLight: AmbientLight? = null
    
    fun addEntity(entity: Entity) {
        entities.add(entity)
    }
    
    fun removeEntity(entity: Entity) {
        entities.remove(entity)
    }
    
    fun getEntities(): List<Entity> = entities.toList()
    
    fun findEntitiesWith(componentType: Class<out Component>): List<Entity> {
        return entities.filter { it.hasComponent(componentType) }
    }
}
