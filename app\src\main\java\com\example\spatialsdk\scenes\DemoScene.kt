package com.example.spatialsdk.scenes

import androidx.compose.ui.graphics.Color
import com.example.spatialsdk.components.*
import com.example.spatialsdk.interaction.InteractableObject
import com.example.spatialsdk.interaction.InteractionSystem

/**
 * Demo scene showcasing various Spatial SDK features
 */
class DemoScene {
    
    fun createScene(): Scene {
        val scene = Scene()
        
        // Setup environment
        setupEnvironment(scene)
        
        // Create interactive objects
        createInteractiveObjects(scene)
        
        // Add lighting
        setupLighting(scene)
        
        // Add audio sources
        setupAudio(scene)
        
        return scene
    }
    
    private fun setupEnvironment(scene: Scene) {
        // Create ground plane
        val groundEntity = Entity.create()
        
        groundEntity.setComponent(
            MeshComponent(
                mesh = PrimitiveMesh.createPlane(),
                material = Material.createPBR().apply {
                    baseColor = Color(0.3f, 0.3f, 0.3f)
                    roughness = 0.8f
                    metallic = 0.1f
                }
            )
        )
        
        groundEntity.setComponent(
            Transform(
                translation = Vector3(0f, -1f, 0f),
                scale = Vector3(10f, 1f, 10f)
            )
        )
        
        // Add physics to ground
        groundEntity.setComponent(
            RigidBodyComponent(
                mass = 0f, // Static body
                isKinematic = true
            )
        )
        
        scene.addEntity(groundEntity)
        
        // Create skybox/environment
        createSkybox(scene)
    }
    
    private fun createSkybox(scene: Scene) {
        val skyboxEntity = Entity.create()
        
        skyboxEntity.setComponent(
            MeshComponent(
                mesh = PrimitiveMesh.createSphere(),
                material = Material.createUnlit(Color(0.5f, 0.7f, 1.0f))
            )
        )
        
        skyboxEntity.setComponent(
            Transform(
                scale = Vector3(50f, 50f, 50f)
            )
        )
        
        scene.addEntity(skyboxEntity)
    }
    
    private fun createInteractiveObjects(scene: Scene) {
        // Create a collection of interactive cubes
        createCubeCollection(scene)
        
        // Create floating spheres
        createFloatingSpheres(scene)
        
        // Create interactive panels
        createInteractivePanels(scene)
        
        // Create animated objects
        createAnimatedObjects(scene)
    }
    
    private fun createCubeCollection(scene: Scene) {
        val colors = listOf(
            Color.Red, Color.Green, Color.Blue, 
            Color.Yellow, Color.Magenta, Color.Cyan
        )
        
        for (i in 0 until 6) {
            val cubeEntity = Entity.create()
            
            cubeEntity.setComponent(
                MeshComponent(
                    mesh = PrimitiveMesh.createCube(),
                    material = Material.createPBR().apply {
                        baseColor = colors[i]
                        metallic = 0.2f
                        roughness = 0.4f
                    }
                )
            )
            
            val angle = (i * 60f) * (Math.PI / 180f)
            val radius = 2f
            val x = (radius * kotlin.math.cos(angle)).toFloat()
            val z = (radius * kotlin.math.sin(angle)).toFloat()
            
            cubeEntity.setComponent(
                Transform(
                    translation = Vector3(x, 0.5f, z),
                    scale = Vector3(0.3f, 0.3f, 0.3f)
                )
            )
            
            cubeEntity.setComponent(
                RigidBodyComponent(
                    mass = 1.0f,
                    isKinematic = false
                )
            )
            
            cubeEntity.setComponent(
                InteractableComponent(
                    onGrab = { println("Cube $i grabbed!") },
                    onRelease = { println("Cube $i released!") }
                )
            )
            
            scene.addEntity(cubeEntity)
        }
    }
    
    private fun createFloatingSpheres(scene: Scene) {
        for (i in 0 until 3) {
            val sphereEntity = Entity.create()
            
            sphereEntity.setComponent(
                MeshComponent(
                    mesh = PrimitiveMesh.createSphere(),
                    material = Material.createPBR().apply {
                        baseColor = Color(1f, 0.5f, 0f) // Orange
                        metallic = 0.8f
                        roughness = 0.1f
                    }
                )
            )
            
            sphereEntity.setComponent(
                Transform(
                    translation = Vector3(-3f + i * 3f, 2f, -1f),
                    scale = Vector3(0.2f, 0.2f, 0.2f)
                )
            )
            
            // Add floating animation
            sphereEntity.setComponent(
                AnimationComponent(
                    animationClip = AnimationClip("float", 3f),
                    isPlaying = true,
                    loop = true
                )
            )
            
            scene.addEntity(sphereEntity)
        }
    }
    
    private fun createInteractivePanels(scene: Scene) {
        // Create a virtual screen/panel
        val panelEntity = Entity.create()
        
        panelEntity.setComponent(
            MeshComponent(
                mesh = PrimitiveMesh.createPlane(),
                material = Material.createUnlit(Color.White)
            )
        )
        
        panelEntity.setComponent(
            Transform(
                translation = Vector3(0f, 1.5f, -3f),
                scale = Vector3(2f, 1.2f, 1f)
            )
        )
        
        panelEntity.setComponent(
            InteractableComponent(
                onHover = { println("Panel hovered") },
                onUnhover = { println("Panel unhovered") }
            )
        )
        
        scene.addEntity(panelEntity)
    }
    
    private fun createAnimatedObjects(scene: Scene) {
        // Create a rotating cylinder
        val cylinderEntity = Entity.create()
        
        cylinderEntity.setComponent(
            MeshComponent(
                mesh = PrimitiveMesh.createCylinder(),
                material = Material.createPBR().apply {
                    baseColor = Color(0.5f, 0f, 0.5f) // Purple
                    metallic = 0.6f
                    roughness = 0.3f
                }
            )
        )
        
        cylinderEntity.setComponent(
            Transform(
                translation = Vector3(3f, 1f, -2f),
                scale = Vector3(0.3f, 0.6f, 0.3f)
            )
        )
        
        cylinderEntity.setComponent(
            AnimationComponent(
                animationClip = AnimationClip("rotate", 4f),
                isPlaying = true,
                loop = true
            )
        )
        
        scene.addEntity(cylinderEntity)
    }
    
    private fun setupLighting(scene: Scene) {
        // Main directional light (sun)
        val sunEntity = Entity.create()
        
        sunEntity.setComponent(
            DirectionalLightComponent(
                color = Color(1f, 0.95f, 0.8f),
                intensity = 1.2f,
                castShadows = true
            )
        )
        
        sunEntity.setComponent(
            Transform(
                rotation = Quaternion.fromEuler(-30f, 45f, 0f)
            )
        )
        
        scene.addEntity(sunEntity)
        
        // Fill light
        val fillLightEntity = Entity.create()
        
        fillLightEntity.setComponent(
            DirectionalLightComponent(
                color = Color(0.7f, 0.8f, 1f),
                intensity = 0.3f,
                castShadows = false
            )
        )
        
        fillLightEntity.setComponent(
            Transform(
                rotation = Quaternion.fromEuler(30f, -45f, 0f)
            )
        )
        
        scene.addEntity(fillLightEntity)
        
        // Ambient lighting
        scene.ambientLight = AmbientLight(
            color = Color(0.4f, 0.4f, 0.5f),
            intensity = 0.2f
        )
    }
    
    private fun setupAudio(scene: Scene) {
        // Background ambient sound
        val ambientAudioEntity = Entity.create()
        
        ambientAudioEntity.setComponent(
            AudioSourceComponent(
                audioClip = AudioClip("ambient_forest.ogg"),
                volume = 0.3f,
                spatialize = false,
                autoPlay = true
            )
        )
        
        scene.addEntity(ambientAudioEntity)
        
        // Spatial audio source
        val spatialAudioEntity = Entity.create()
        
        spatialAudioEntity.setComponent(
            Transform(
                translation = Vector3(2f, 1f, -1f)
            )
        )
        
        spatialAudioEntity.setComponent(
            AudioSourceComponent(
                audioClip = AudioClip("water_fountain.ogg"),
                volume = 0.8f,
                spatialize = true,
                autoPlay = true
            )
        )
        
        scene.addEntity(spatialAudioEntity)
    }
}

/**
 * Scene manager for handling multiple scenes
 */
class SceneManager {
    private var currentScene: Scene? = null
    private val scenes = mutableMapOf<String, Scene>()
    
    fun registerScene(name: String, scene: Scene) {
        scenes[name] = scene
    }
    
    fun loadScene(name: String): Scene? {
        val scene = scenes[name]
        if (scene != null) {
            currentScene = scene
            println("Loaded scene: $name")
        } else {
            println("Scene not found: $name")
        }
        return scene
    }
    
    fun getCurrentScene(): Scene? = currentScene
    
    fun createDemoScene(): Scene {
        val demoScene = DemoScene()
        val scene = demoScene.createScene()
        registerScene("demo", scene)
        return scene
    }
}
