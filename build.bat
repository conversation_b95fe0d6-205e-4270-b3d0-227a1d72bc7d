@echo off
setlocal enabledelayedexpansion

REM Meta Spatial SDK Demo Build Script for Windows
REM This script helps build and deploy the Spatial SDK demo application

echo 🚀 Meta Spatial SDK Demo Build Script
echo ======================================

:check_android_sdk
echo [INFO] Checking Android SDK...
where adb >nul 2>nul
if %errorlevel% neq 0 (
    echo [ERROR] ADB not found. Please install Android SDK and add it to PATH.
    pause
    exit /b 1
)
echo [SUCCESS] Android SDK found

:check_device
echo [INFO] Checking for connected devices...
for /f %%i in ('adb devices ^| find /c "device"') do set device_count=%%i
if %device_count% leq 1 (
    echo [WARNING] No devices connected. Please connect your Meta Quest device.
    echo [INFO] Make sure:
    echo [INFO]   1. Developer mode is enabled on your Quest
    echo [INFO]   2. USB debugging is enabled
    echo [INFO]   3. Device is connected via USB
    pause
    exit /b 1
)
echo [SUCCESS] Device connected

:main_menu
echo.
echo Please select an option:
echo 1^) Full build and deploy
echo 2^) Clean build
echo 3^) Build APK only
echo 4^) Install APK
echo 5^) Launch app
echo 6^) Show logs
echo 7^) Check device status
echo 8^) Exit
echo.
set /p choice="Enter your choice [1-8]: "

if "%choice%"=="1" goto full_deploy
if "%choice%"=="2" goto clean_build
if "%choice%"=="3" goto build_apk
if "%choice%"=="4" goto install_apk
if "%choice%"=="5" goto launch_app
if "%choice%"=="6" goto show_logs
if "%choice%"=="7" goto check_status
if "%choice%"=="8" goto exit_script
echo [ERROR] Invalid option. Please try again.
goto main_menu

:full_deploy
call :check_android_sdk
call :check_device
call :clean_build_func
call :build_apk_func
call :install_apk_func
call :launch_app_func
echo [SUCCESS] Full deployment completed!
goto continue

:clean_build
call :clean_build_func
call :build_apk_func
goto continue

:build_apk
call :build_apk_func
goto continue

:install_apk
call :check_device
call :install_apk_func
goto continue

:launch_app
call :check_device
call :launch_app_func
goto continue

:show_logs
call :check_device
call :show_logs_func
goto continue

:check_status
call :check_android_sdk
call :check_device
echo [SUCCESS] Device is ready for development
goto continue

:clean_build_func
echo [INFO] Cleaning previous build...
gradlew.bat clean
if %errorlevel% neq 0 (
    echo [ERROR] Clean failed
    pause
    exit /b 1
)
echo [SUCCESS] Clean completed
exit /b 0

:build_apk_func
echo [INFO] Building APK...
gradlew.bat assembleDebug
if %errorlevel% neq 0 (
    echo [ERROR] Build failed
    pause
    exit /b 1
)
echo [SUCCESS] APK built successfully
exit /b 0

:install_apk_func
echo [INFO] Installing APK to device...
gradlew.bat installDebug
if %errorlevel% neq 0 (
    echo [ERROR] Installation failed
    pause
    exit /b 1
)
echo [SUCCESS] APK installed successfully
exit /b 0

:launch_app_func
echo [INFO] Launching application...
adb shell am start -n com.example.spatialsdk/.MainActivity
if %errorlevel% neq 0 (
    echo [ERROR] Launch failed
    pause
    exit /b 1
)
echo [SUCCESS] Application launched
exit /b 0

:show_logs_func
echo [INFO] Showing application logs (Ctrl+C to stop)...
adb logcat | findstr "SpatialSDK MainActivity SPATIAL"
exit /b 0

:continue
echo.
pause
goto main_menu

:exit_script
echo [INFO] Goodbye!
pause
exit /b 0
