# Meta Spatial SDK 真实集成指南

本文档说明如何将此架构演示项目转换为真实的 Meta Spatial SDK 应用。

## 当前状态

此项目目前是一个**架构演示版本**，包含：
- ✅ 完整的项目结构
- ✅ ECS 架构设计
- ✅ 模拟的 API 接口
- ✅ Jetpack Compose UI
- ✅ 手部追踪系统架构
- ✅ 物理交互系统设计
- ✅ 空间音频架构

## 集成真实 Meta Spatial SDK 的步骤

### 1. 获取 Meta Spatial SDK 访问权限

1. **注册 Meta 开发者账号**
   - 访问 [Meta for Developers](https://developers.meta.com/)
   - 创建开发者账号
   - 申请 Spatial SDK 访问权限

2. **下载 Meta Spatial SDK**
   - 从 [Meta Horizon Downloads](https://developers.meta.com/horizon/downloads/spatial-sdk/) 下载 SDK
   - 获取 SDK 文档和示例代码

### 2. 更新构建配置

#### 2.1 更新 `build.gradle.kts` (项目级别)

```kotlin
// 添加 Meta 仓库
buildscript {
    repositories {
        google()
        mavenCentral()
        maven {
            url = uri("https://artifactory.oculus.com/artifactory/maven")
        }
    }
    dependencies {
        classpath("com.android.tools.build:gradle:8.2.0")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin:1.9.10")
        classpath("com.meta.spatial:plugin:0.6.1") // 添加 Meta 插件
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
        maven {
            url = uri("https://artifactory.oculus.com/artifactory/maven")
        }
    }
}
```

#### 2.2 更新 `app/build.gradle.kts`

```kotlin
plugins {
    id("com.android.application")
    id("org.jetbrains.kotlin.android")
    id("com.meta.spatial.plugin") // 添加 Meta 插件
}

dependencies {
    // 添加真实的 Meta Spatial SDK
    implementation("com.meta.spatial:sdk:0.6.1")
    
    // 其他依赖保持不变...
}
```

### 3. 替换模拟 API

#### 3.1 更新导入语句

将模拟的导入：
```kotlin
import com.example.spatialsdk.core.SpatialEnvironment
import com.example.spatialsdk.components.*
```

替换为真实的 Meta SDK 导入：
```kotlin
import com.meta.spatial.core.*
import com.meta.spatial.toolkit.*
import com.meta.spatial.vr.*
```

#### 3.2 更新 SpatialEnvironment 初始化

将模拟的初始化：
```kotlin
spatialEnvironment = SpatialEnvironment.create(this)
```

替换为真实的 Meta SDK 初始化：
```kotlin
spatialEnvironment = SpatialEnvironment.create(this)
```

#### 3.3 更新组件系统

将模拟的组件：
```kotlin
// 模拟版本
data class Transform(
    val translation: Vector3 = Vector3.zero(),
    val rotation: Quaternion = Quaternion.identity(),
    val scale: Vector3 = Vector3.one()
) : Component
```

替换为真实的 Meta SDK 组件：
```kotlin
// Meta SDK 版本
import com.meta.spatial.core.Transform
import com.meta.spatial.core.Vector3
import com.meta.spatial.core.Quaternion
```

### 4. 更新手部追踪

#### 4.1 替换手部追踪管理器

将模拟的手部追踪：
```kotlin
// 模拟版本
class HandTrackingManager {
    var isEnabled: Boolean = false
    var onHandUpdate: ((leftHand: Hand?, rightHand: Hand?) -> Unit)? = null
}
```

替换为真实的 Meta SDK 手部追踪：
```kotlin
// Meta SDK 版本
import com.meta.spatial.vr.HandTracker
import com.meta.spatial.vr.Hand

val handTracker = spatialEnvironment.getHandTracker()
handTracker.isEnabled = true
handTracker.onHandUpdate = { leftHand, rightHand ->
    // 处理手部追踪数据
}
```

### 5. 更新渲染系统

#### 5.1 替换渲染引擎

将模拟的渲染引擎：
```kotlin
// 模拟版本
class RenderingEngine {
    fun initialize(context: Context) { /* 模拟实现 */ }
    fun setVRMode(enabled: Boolean) { /* 模拟实现 */ }
}
```

替换为真实的 Meta SDK 渲染：
```kotlin
// Meta SDK 版本
import com.meta.spatial.core.Scene
import com.meta.spatial.core.Entity
import com.meta.spatial.core.MeshRenderer

// 使用 Meta SDK 的渲染系统
val scene = spatialEnvironment.scene
val entity = Entity.create()
entity.setComponent(MeshRenderer(mesh, material))
scene.addEntity(entity)
```

### 6. 更新物理系统

#### 6.1 替换物理引擎

将模拟的物理引擎：
```kotlin
// 模拟版本
class PhysicsEngine {
    var isEnabled = false
    fun initialize() { /* 模拟实现 */ }
}
```

替换为真实的 Meta SDK 物理：
```kotlin
// Meta SDK 版本
import com.meta.spatial.physics.RigidBody
import com.meta.spatial.physics.Collider

val rigidBody = entity.setComponent(RigidBody(mass = 1.0f))
val collider = entity.setComponent(Collider.createBox(Vector3(1f, 1f, 1f)))
```

### 7. 更新音频系统

#### 7.1 替换音频引擎

将模拟的音频引擎：
```kotlin
// 模拟版本
class AudioEngine {
    fun setSpatialMode(enabled: Boolean) { /* 模拟实现 */ }
}
```

替换为真实的 Meta SDK 音频：
```kotlin
// Meta SDK 版本
import com.meta.spatial.audio.AudioSource
import com.meta.spatial.audio.SpatialAudio

val audioSource = entity.setComponent(AudioSource(audioClip))
audioSource.spatialize = true
audioSource.play()
```

### 8. 测试和调试

#### 8.1 设备要求
- Meta Quest 2/3/3S/Pro
- 开发者模式已启用
- Meta Quest build v69.0 或更新

#### 8.2 调试工具
- Meta Quest Developer Hub (MQDH)
- OVR Metrics Tool
- Android Studio 调试器

### 9. 常见问题解决

#### 9.1 SDK 访问问题
```
错误：Could not resolve com.meta.spatial:sdk:0.6.1
解决：确保已获得 Meta SDK 访问权限并正确配置仓库
```

#### 9.2 权限问题
```
错误：Hand tracking permission denied
解决：在 AndroidManifest.xml 中添加必要权限
```

#### 9.3 设备兼容性
```
错误：VR features not supported
解决：确保设备支持 VR 功能并已启用开发者模式
```

### 10. 参考资源

- [Meta Spatial SDK 官方文档](https://developers.meta.com/horizon/develop/spatial-sdk)
- [Meta Spatial SDK 示例](https://github.com/meta-quest/Meta-Spatial-SDK-Samples)
- [Meta 开发者社区](https://communityforums.atmeta.com/)
- [OpenXR 规范](https://www.khronos.org/openxr/)

### 11. 迁移检查清单

- [ ] 获得 Meta 开发者账号
- [ ] 下载 Meta Spatial SDK
- [ ] 更新构建配置文件
- [ ] 替换模拟 API 导入
- [ ] 更新 SpatialEnvironment 初始化
- [ ] 替换组件系统
- [ ] 更新手部追踪实现
- [ ] 替换渲染系统
- [ ] 更新物理系统
- [ ] 替换音频系统
- [ ] 测试在真实设备上运行
- [ ] 优化性能和用户体验

完成以上步骤后，您将拥有一个完全功能的 Meta Spatial SDK 应用！
