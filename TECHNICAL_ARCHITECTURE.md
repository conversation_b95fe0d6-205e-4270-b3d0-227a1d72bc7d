# Spatial SDK 技术架构设计

## 整体架构

### 分层架构
```
┌─────────────────────────────────────┐
│           Application Layer         │  # 开发者应用
├─────────────────────────────────────┤
│           SDK API Layer             │  # 公开 API
├─────────────────────────────────────┤
│          Framework Layer            │  # 框架层
│  ┌─────────┬─────────┬─────────────┐ │
│  │   ECS   │ Scene   │   Asset     │ │
│  │ System  │ Graph   │  Manager    │ │
│  └─────────┴─────────┴─────────────┘ │
├─────────────────────────────────────┤
│           Engine Layer              │  # 引擎层
│  ┌─────────┬─────────┬─────────────┐ │
│  │Rendering│ Physics │    Audio    │ │
│  │ Engine  │ Engine  │   Engine    │ │
│  └─────────┴─────────┴─────────────┘ │
├─────────────────────────────────────┤
│          Platform Layer             │  # 平台层
│  ┌─────────┬─────────┬─────────────┐ │
│  │ OpenXR  │ Android │    Input    │ │
│  │   API   │   NDK   │   System    │ │
│  └─────────┴─────────┴─────────────┘ │
├─────────────────────────────────────┤
│           Hardware Layer            │  # 硬件层
│     VR Headsets, Controllers       │
└─────────────────────────────────────┘
```

## 核心模块设计

### 1. ECS 系统

#### 1.1 组件接口
```kotlin
interface Component {
    val componentId: Int
}

// 基础组件
data class Transform(
    var position: Vector3 = Vector3.ZERO,
    var rotation: Quaternion = Quaternion.IDENTITY,
    var scale: Vector3 = Vector3.ONE
) : Component

data class MeshRenderer(
    var mesh: Mesh,
    var material: Material,
    var visible: Boolean = true
) : Component

data class RigidBody(
    var mass: Float = 1.0f,
    var isKinematic: Boolean = false,
    var velocity: Vector3 = Vector3.ZERO
) : Component
```

#### 1.2 实体管理
```kotlin
class Entity(val id: Long) {
    private val components = mutableMapOf<Class<out Component>, Component>()
    
    fun <T : Component> addComponent(component: T): T {
        components[component::class.java] = component
        return component
    }
    
    fun <T : Component> getComponent(type: Class<T>): T? {
        return components[type] as? T
    }
    
    fun <T : Component> removeComponent(type: Class<T>) {
        components.remove(type)
    }
}
```

#### 1.3 系统架构
```kotlin
abstract class System {
    abstract fun update(world: World, deltaTime: Float)
    abstract fun getRequiredComponents(): Set<Class<out Component>>
}

class RenderSystem : System() {
    override fun update(world: World, deltaTime: Float) {
        val entities = world.getEntitiesWith(Transform::class.java, MeshRenderer::class.java)
        for (entity in entities) {
            val transform = entity.getComponent(Transform::class.java)!!
            val renderer = entity.getComponent(MeshRenderer::class.java)!!
            renderMesh(transform, renderer)
        }
    }
}
```

### 2. 渲染系统

#### 2.1 渲染器接口
```kotlin
interface Renderer {
    fun initialize()
    fun beginFrame()
    fun setViewMatrix(viewMatrix: Matrix4)
    fun setProjectionMatrix(projMatrix: Matrix4)
    fun renderMesh(mesh: Mesh, material: Material, worldMatrix: Matrix4)
    fun endFrame()
    fun shutdown()
}
```

#### 2.2 OpenGL 实现
```kotlin
class OpenGLRenderer : Renderer {
    private var shaderProgram: Int = 0
    private var viewMatrixLocation: Int = 0
    private var projMatrixLocation: Int = 0
    
    override fun initialize() {
        // 初始化 OpenGL 上下文
        val vertexShader = loadShader(GL_VERTEX_SHADER, vertexShaderSource)
        val fragmentShader = loadShader(GL_FRAGMENT_SHADER, fragmentShaderSource)
        shaderProgram = createProgram(vertexShader, fragmentShader)
        
        viewMatrixLocation = glGetUniformLocation(shaderProgram, "u_ViewMatrix")
        projMatrixLocation = glGetUniformLocation(shaderProgram, "u_ProjectionMatrix")
    }
    
    override fun renderMesh(mesh: Mesh, material: Material, worldMatrix: Matrix4) {
        glUseProgram(shaderProgram)
        
        // 设置矩阵
        glUniformMatrix4fv(viewMatrixLocation, 1, false, viewMatrix.values, 0)
        glUniformMatrix4fv(projMatrixLocation, 1, false, projMatrix.values, 0)
        
        // 绑定顶点数据
        glBindVertexArray(mesh.vao)
        glDrawElements(GL_TRIANGLES, mesh.indexCount, GL_UNSIGNED_INT, 0)
    }
}
```

### 3. 物理系统

#### 3.1 物理世界
```kotlin
interface PhysicsWorld {
    fun addRigidBody(body: RigidBody, entity: Entity)
    fun removeRigidBody(entity: Entity)
    fun stepSimulation(deltaTime: Float)
    fun raycast(from: Vector3, to: Vector3): RaycastResult?
}

class BulletPhysicsWorld : PhysicsWorld {
    private val dynamicsWorld: btDiscreteDynamicsWorld
    private val bodyMap = mutableMapOf<Entity, btRigidBody>()
    
    override fun stepSimulation(deltaTime: Float) {
        dynamicsWorld.stepSimulation(deltaTime, 10)
        
        // 同步物理结果到 Transform 组件
        for ((entity, btBody) in bodyMap) {
            val transform = entity.getComponent(Transform::class.java)
            if (transform != null) {
                val worldTransform = btBody.worldTransform
                transform.position = worldTransform.origin.toVector3()
                transform.rotation = worldTransform.rotation.toQuaternion()
            }
        }
    }
}
```

### 4. 输入系统

#### 4.1 手部追踪
```kotlin
interface HandTracker {
    var isEnabled: Boolean
    fun getLeftHand(): Hand?
    fun getRightHand(): Hand?
    fun setHandUpdateCallback(callback: (Hand?, Hand?) -> Unit)
}

data class Hand(
    val isTracked: Boolean,
    val palmPose: Pose,
    val fingers: List<Finger>,
    val gestures: HandGestures
)

class OpenXRHandTracker : HandTracker {
    override fun getLeftHand(): Hand? {
        // OpenXR 手部追踪实现
        val handTracking = xrSession.getHandTracking(XR_HAND_LEFT)
        return if (handTracking.isActive) {
            Hand(
                isTracked = true,
                palmPose = handTracking.palmPose,
                fingers = handTracking.fingers,
                gestures = detectGestures(handTracking)
            )
        } else null
    }
}
```

### 5. 音频系统

#### 5.1 空间音频
```kotlin
interface AudioEngine {
    fun initialize()
    fun createAudioSource(audioClip: AudioClip): AudioSource
    fun setListenerTransform(transform: Transform)
    fun update()
    fun shutdown()
}

class OpenALAudioEngine : AudioEngine {
    private var device: ALCdevice? = null
    private var context: ALCcontext? = null
    
    override fun createAudioSource(audioClip: AudioClip): AudioSource {
        val sourceId = alGenSources()
        val bufferId = alGenBuffers()
        
        alBufferData(bufferId, audioClip.format, audioClip.data, audioClip.frequency)
        alSourcei(sourceId, AL_BUFFER, bufferId)
        
        return OpenALAudioSource(sourceId, bufferId)
    }
}
```

## 平台集成

### 1. OpenXR 集成
```kotlin
class OpenXRPlatform {
    private var instance: XrInstance? = null
    private var session: XrSession? = null
    private var swapchain: XrSwapchain? = null
    
    fun initialize() {
        // 创建 OpenXR 实例
        instance = xrCreateInstance(instanceCreateInfo)
        
        // 创建会话
        session = xrCreateSession(instance, sessionCreateInfo)
        
        // 创建交换链
        swapchain = xrCreateSwapchain(session, swapchainCreateInfo)
    }
    
    fun beginFrame(): FrameState {
        val frameState = xrWaitFrame(session)
        xrBeginFrame(session)
        return frameState
    }
    
    fun endFrame(layers: List<CompositionLayer>) {
        xrEndFrame(session, frameEndInfo.apply {
            this.layers = layers
        })
    }
}
```

### 2. Android 集成
```kotlin
class AndroidPlatform {
    fun initializeNativeActivity(activity: NativeActivity) {
        // 初始化 EGL 上下文
        val display = eglGetDisplay(EGL_DEFAULT_DISPLAY)
        eglInitialize(display)
        
        // 创建 OpenGL ES 上下文
        val context = eglCreateContext(display, config, EGL_NO_CONTEXT, contextAttribs)
        
        // 设置当前上下文
        eglMakeCurrent(display, surface, surface, context)
    }
}
```

## 性能优化

### 1. 渲染优化
- **批处理**: 合并相同材质的网格
- **剔除**: 视锥剔除、遮挡剔除
- **LOD**: 距离级别细节
- **实例化**: GPU 实例化渲染

### 2. 内存优化
- **对象池**: 减少 GC 压力
- **内存映射**: 大文件处理
- **压缩**: 纹理、音频压缩

### 3. 多线程
- **渲染线程**: 独立渲染线程
- **物理线程**: 异步物理计算
- **加载线程**: 后台资源加载

## 工具链

### 1. 场景编辑器
```kotlin
class SceneEditor {
    fun loadScene(path: String): Scene
    fun saveScene(scene: Scene, path: String)
    fun addEntity(entity: Entity)
    fun removeEntity(entity: Entity)
    fun selectEntity(entity: Entity)
    fun transformEntity(entity: Entity, transform: Transform)
}
```

### 2. 资源管理
```kotlin
class AssetManager {
    private val loadedAssets = mutableMapOf<String, Asset>()
    
    fun <T : Asset> loadAsset(path: String, type: Class<T>): T {
        return loadedAssets.getOrPut(path) {
            when (type) {
                Mesh::class.java -> loadMesh(path)
                Texture::class.java -> loadTexture(path)
                AudioClip::class.java -> loadAudioClip(path)
                else -> throw IllegalArgumentException("Unsupported asset type")
            }
        } as T
    }
}
```

这个技术架构为您提供了构建 Spatial SDK 的完整蓝图，涵盖了从底层平台集成到高层 API 设计的所有关键组件。
